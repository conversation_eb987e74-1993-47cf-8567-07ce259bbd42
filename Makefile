# Simple Makefile for a Go project

GOOS ?= linux
GOARCH ?= amd64

# Build the application
all: build

build:
	@echo "Building for $(GOOS)/$(GOARCH)..."
	@GOOS=$(GOOS) GOARCH=$(GOARCH) go build -o main cmd/api/main.go

build-mac:
	@echo "Building for darwin/arm64..."
		@GOOS=darwin GOARCH=arm64 go build -o main cmd/api/main.go

# Run the application
run:
	@go run cmd/api/main.go

# Test the application
test:
	@echo "Testing..."
	@go test ./tests/... ./internal/... -v

# Run tests with coverage
test-coverage:
	@echo "Running tests with coverage..."
	@go test ./tests/... ./internal/... -coverprofile=coverage.out -v
	@go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Run specific tests
test-server:
	@echo "Testing server package..."
	@go test ./tests/server/... -v

test-db:
	@echo "Testing db package..."
	@go test ./tests/db/... -v

# Clean the binary
clean:
	@echo "Cleaning..."
	@rm -f main
	@rm -f coverage.out coverage.html

# Live Reload
watch:
	@if command -v air > /dev/null; then \
	    air; \
	    echo "Watching...";\
	else \
	    read -p "Go's 'air' is not installed on your machine. Do you want to install it? [Y/n] " choice; \
	    if [ "$$choice" != "n" ] && [ "$$choice" != "N" ]; then \
	        go install github.com/air-verse/air@latest; \
	        air; \
	        echo "Watching...";\
	    else \
	        echo "You chose not to install air. Exiting..."; \
	        exit 1; \
	    fi; \
	fi

.PHONY: all build run test test-coverage test-server test-db clean
