package testutil

import (
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
)

// SetEnvVar sets an environment variable for testing and returns a function to restore the original value
func SetEnvVar(key, value string) func() {
	original := os.Getenv(key)
	os.Setenv(key, value)
	return func() {
		if original != "" {
			os.Setenv(key, original)
		} else {
			os.Unsetenv(key)
		}
	}
}

// CreateTestRequest creates a test HTTP request with the specified method and path
func CreateTestRequest(method, path string, body string) (*http.Request, *httptest.ResponseRecorder) {
	req := httptest.NewRequest(method, path, nil)
	w := httptest.NewRecorder()
	return req, w
}

// SkipIfNoDocker skips a test if Docker is not available
func SkipIfNoDocker(t *testing.T) {
	_, err := os.Stat("/var/run/docker.sock")
	if os.IsNotExist(err) {
		t.Ski<PERSON>("Docker socket not available, skipping test")
	}
}

// SkipIfCI skips a test if running in a CI environment
func SkipIfCI(t *testing.T) {
	if os.Getenv("CI") == "true" {
		t.Skip("Skipping test in CI environment")
	}
}

// TempEnvVars sets multiple environment variables for a test and returns a function to restore the original values
func TempEnvVars(t *testing.T, vars map[string]string) func() {
	originals := make(map[string]string)

	for k, v := range vars {
		originals[k] = os.Getenv(k)
		os.Setenv(k, v)
	}

	return func() {
		for k, v := range originals {
			if v != "" {
				os.Setenv(k, v)
			} else {
				os.Unsetenv(k)
			}
		}
	}
}
