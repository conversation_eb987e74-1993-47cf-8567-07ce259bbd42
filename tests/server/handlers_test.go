package server_test

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gardinerbk/probelet/internal/server"
	"github.com/stretchr/testify/assert"
)

func TestPingHandler(t *testing.T) {
	// Create a request to pass to our handler
	req := httptest.NewRequest("GET", "/ping", nil)
	w := httptest.NewRecorder()

	// Create a server instance
	s := &server.Server{}

	// Call the handler directly
	s.<PERSON><PERSON>(w, req)

	// Check the status code
	assert.Equal(t, http.StatusOK, w.Code)

	// Check the response body contains "pong"
	assert.Contains(t, w.Body.String(), "pong")
}

func TestHealthHandler(t *testing.T) {
	// Create a request to pass to our handler
	req := httptest.NewRequest("GET", "/health", nil)
	w := httptest.NewRecorder()

	// Create a server instance
	s := &server.Server{}

	// Call the handler directly
	s.<PERSON>Handler(w, req)

	// Check the status code is 200 OK
	assert.Equal(t, http.StatusOK, w.Code)
}

// Additional test for error handlers
func TestInternalServerErrorHandler(t *testing.T) {
	// Create a request to pass to our handler
	req := httptest.NewRequest("GET", "/error", nil)
	w := httptest.NewRecorder()

	// Call the handler
	server.InternalServerErrorHandler(w, req)

	// Check the status code is 500 Internal Server Error
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestNotFoundHandler(t *testing.T) {
	// Create a request to pass to our handler
	req := httptest.NewRequest("GET", "/notfound", nil)
	w := httptest.NewRecorder()

	// Call the handler
	server.NotFoundHandler(w, req)

	// Current implementation returns 500 instead of 404
	// This should be updated in the future to return 404
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestBadRequestHandler(t *testing.T) {
	// Create a request to pass to our handler
	req := httptest.NewRequest("GET", "/badrequest", nil)
	w := httptest.NewRecorder()

	// Call the handler
	server.BadRequestHandler(w, req)

	// Current implementation returns 500 instead of 400
	// This should be updated in the future to return 400
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}
