package logger

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strings"
	"time"
)

type Level string

const (
	INFO  Level = "INFO"
	ERROR Level = "ERROR"
	DEBUG Level = "DEBUG"
	WARN  Level = "WARN"
)

var (
	logger          *log.Logger
	fileLogger      *log.Logger
	logFile         *os.File
	currentLogLevel Level
)

func Init() error {
	// Create logs directory if it doesn't exist
	err := os.MkdirAll("logs", 0755)
	if err != nil {
		return fmt.Errorf("failed to create logs directory: %v", err)
	}

	// Create log file with timestamp
	// timestamp := time.Now().Format("2006-01-02")
	// logPath := filepath.Join("logs", fmt.Sprintf("probelet-%s.log", timestamp))

	logFile, err = os.OpenFile("probelet.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return fmt.<PERSON><PERSON><PERSON>("failed to open log file: %v", err)
	}

	// Initialize both console and file loggers
	logger = log.New(os.Stdout, "", log.Ldate|log.Ltime|log.Lmicroseconds)
	fileLogger = log.New(logFile, "", log.Ldate|log.Ltime|log.Lmicroseconds)

	Info("Logging initialized", Fields{
		"logFile": "probelet.log",
	})

	currentLogLevel = INFO

	return nil
}

func Cleanup() {
	if logFile != nil {
		logFile.Close()
	}
}

type Fields map[string]interface{}

func Log(level Level, message string, fields Fields) {
	if logger == nil {
		if err := Init(); err != nil {
			log.Printf("Failed to initialize logger: %v", err)
			return
		}
	}

	// Basic structured log format
	logEntry := struct {
		Timestamp string                 `json:"timestamp"`
		Level     string                 `json:"level"`
		Message   string                 `json:"message"`
		Fields    map[string]interface{} `json:"fields,omitempty"`
	}{
		Timestamp: time.Now().UTC().Format(time.RFC3339),
		Level:     string(level),
		Message:   message,
		Fields:    fields,
	}

	jsonData, err := json.Marshal(logEntry)
	if err != nil {
		log.Printf("Error marshaling log entry: %v", err)
		return
	}

	// Write to both console and file
	logger.Println(string(jsonData))
	if fileLogger != nil {
		fileLogger.Println(string(jsonData))
	}
}

// Helper functions
func Info(message string, fields Fields) {
	Log(INFO, message, fields)
}

func Error(message string, fields Fields) {
	Log(ERROR, message, fields)
}

func Debug(message string, fields Fields) {
	Log(DEBUG, message, fields)
}

func Warn(message string, fields Fields) {
	Log(WARN, message, fields)
}

// SetLogLevel sets the logging level
func SetLogLevel(level string) {
	switch strings.ToLower(level) {
	case "debug":
		currentLogLevel = DEBUG
	case "info":
		currentLogLevel = INFO
	case "warn":
		currentLogLevel = WARN
	case "error":
		currentLogLevel = ERROR
	default:
		currentLogLevel = INFO
	}

	Info("Log level set", Fields{
		"level": string(currentLogLevel),
	})
}
